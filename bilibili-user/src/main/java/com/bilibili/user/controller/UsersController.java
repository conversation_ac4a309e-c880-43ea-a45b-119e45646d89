package com.bilibili.user.controller;
import com.bilibili.common.context.UserContextHolder;
import com.bilibili.common.response.ApiResponse;
import com.bilibili.user.entity.po.Users;
import com.bilibili.user.entity.vo.UserInfoVO;
import com.bilibili.user.service.IUsersService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 用户基础信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "用户基础信息表", description = "用户基础信息表相关接口")
public class UsersController {
    private final IUsersService usersService;
    @GetMapping("/me")
    @Operation(summary = "获取用户信息", description = "获取当前登录用户的信息")
    public ApiResponse<UserInfoVO> getUserInfo() {
        // 从UserContext获取当前用户信息
        Long currentUserId = UserContextHolder.getCurrentUserId();
        String currentUsername = UserContextHolder.getCurrentUsername();

        // 检查用户是否已认证
        if (currentUserId == null || currentUsername == null) {
            log.warn("用户未认证，无法获取用户信息");
            return ApiResponse.error(401, "用户未认证，请先登录");
        }

        log.info("获取用户信息: userId={}, username={}", currentUserId, currentUsername);
        UserInfoVO userInfoVO = usersService.getUserInfo(currentUsername);

        if (userInfoVO == null) {
            log.warn("用户信息不存在: {}", currentUsername);
            return ApiResponse.error(404, "用户信息不存在");
        }
        return ApiResponse.success("获取用户信息成功", userInfoVO);
    }

    @GetMapping("/{uid}")
    @Operation(summary ="获取用户信息",description = "根据uid获取用户信息")
    public ApiResponse<Users> getUserInfo(@PathVariable Long uid) {
        log.info("获取用户id信息: {}", uid);
        Users user = usersService.getUserInfoByUid(uid);
        if (user == null) {
            log.warn("用户信息不存在: {}", uid);
            return ApiResponse.error(404, "用户信息不存在");
        }
        return ApiResponse.success("获取用户信息成功", user);
    }
}
