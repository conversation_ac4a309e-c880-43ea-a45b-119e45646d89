package com.bilibili.api.client;

import com.bilibili.api.dto.AiChatRequestDTO;
import com.bilibili.api.dto.AiChatResponseDTO;
import com.bilibili.api.dto.ContentAnalysisDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * AI服务Feign客户端
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@FeignClient(name = "bilibili-aiassistant", path = "/api/v1/ai")
public interface AiServiceClient {

    /**
     * AI聊天对话
     *
     * @param request 聊天请求
     * @return 聊天响应
     */
    @PostMapping("/chat")
    AiChatResponseDTO chat(@RequestBody AiChatRequestDTO request);

    /**
     * 分析视频内容
     *
     * @param videoId 视频ID
     * @return 分析结果
     */
    @PostMapping("/analyze/video")
    ContentAnalysisDTO analyzeVideo(@RequestParam("videoId") String videoId);

    /**
     * 分析用户行为
     *
     * @param userId 用户ID
     * @return 分析结果
     */
    @PostMapping("/analyze/user")
    ContentAnalysisDTO analyzeUser(@RequestParam("userId") Long userId);

    /**
     * 获取推荐内容
     *
     * @param userId 用户ID
     * @param type   推荐类型
     * @param limit  推荐数量
     * @return 推荐列表
     */
    @GetMapping("/recommend")
    List<String> getRecommendations(@RequestParam("userId") Long userId,
                                    @RequestParam("type") String type,
                                    @RequestParam(value = "limit", defaultValue = "10") Integer limit);

    /**
     * 语义搜索
     *
     * @param query       查询文本
     * @param contentType 内容类型
     * @param limit       返回数量
     * @return 搜索结果
     */
    @GetMapping("/search/semantic")
    List<String> semanticSearch(@RequestParam("query") String query,
                                @RequestParam("contentType") String contentType,
                                @RequestParam(value = "limit", defaultValue = "10") Integer limit);

    /**
     * 生成内容摘要
     *
     * @param content 内容文本
     * @return 摘要
     */
    @PostMapping("/summarize")
    String summarize(@RequestParam("content") String content);

    /**
     * 提取内容标签
     *
     * @param content 内容文本
     * @return 标签列表
     */
    @PostMapping("/extract-tags")
    List<String> extractTags(@RequestParam("content") String content);

    /**
     * 情感分析
     *
     * @param content 内容文本
     * @return 情感分析结果
     */
    @PostMapping("/sentiment")
    String analyzeSentiment(@RequestParam("content") String content);
}
