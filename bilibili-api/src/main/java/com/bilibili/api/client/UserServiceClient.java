package com.bilibili.api.client;

import com.bilibili.api.dto.UserInfoDTO;
import com.bilibili.api.dto.UserStatsDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 用户服务Feign客户端
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@FeignClient(name = "bilibili-user", path = "/api/v1/user")
public interface UserServiceClient {

    /**
     * 根据用户ID获取用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    @GetMapping("/info/{userId}")
    UserInfoDTO getUserInfo(@PathVariable("userId") Long userId);

    /**
     * 根据用户名获取用户信息
     *
     * @param username 用户名
     * @return 用户信息
     */
    @GetMapping("/info/by-username")
    UserInfoDTO getUserInfoByUsername(@RequestParam("username") String username);

    /**
     * 获取用户统计信息
     *
     * @param userId 用户ID
     * @return 用户统计信息
     */
    @GetMapping("/stats/{userId}")
    UserStatsDTO getUserStats(@PathVariable("userId") Long userId);

    /**
     * 检查用户是否存在
     *
     * @param userId 用户ID
     * @return 是否存在
     */
    @GetMapping("/exists/{userId}")
    Boolean userExists(@PathVariable("userId") Long userId);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    @GetMapping("/exists/username")
    Boolean usernameExists(@RequestParam("username") String username);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @return 是否存在
     */
    @GetMapping("/exists/email")
    Boolean emailExists(@RequestParam("email") String email);

    /**
     * 检查手机号是否存在
     *
     * @param phone 手机号
     * @return 是否存在
     */
    @GetMapping("/exists/phone")
    Boolean phoneExists(@RequestParam("phone") String phone);
}
