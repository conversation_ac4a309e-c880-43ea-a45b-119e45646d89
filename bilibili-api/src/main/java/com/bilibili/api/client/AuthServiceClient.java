package com.bilibili.api.client;

import com.bilibili.api.dto.TokenValidationDTO;
import com.bilibili.api.dto.UserInfoDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 认证服务Feign客户端
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@FeignClient(name = "bilibili-auth", path = "/api/v1/auth")
public interface AuthServiceClient {

    /**
     * 验证访问令牌
     *
     * @param token 访问令牌
     * @return 令牌验证结果
     */
    @PostMapping("/validate-token")
    TokenValidationDTO validateToken(@RequestParam("token") String token);

    /**
     * 根据令牌获取用户信息
     *
     * @param token 访问令牌
     * @return 用户信息
     */
    @GetMapping("/user-info")
    UserInfoDTO getUserInfoByToken(@RequestParam("token") String token);

    /**
     * 发送短信验证码
     *
     * @param phone 手机号
     * @param type  验证码类型
     * @return 发送结果
     */
    @PostMapping("/send-sms-code")
    Boolean sendSmsCode(@RequestParam("phone") String phone,
                        @RequestParam("type") String type);

    /**
     * 验证短信验证码
     *
     * @param phone 手机号
     * @param code  验证码
     * @param type  验证码类型
     * @return 验证结果
     */
    @PostMapping("/verify-sms-code")
    Boolean verifySmsCode(@RequestParam("phone") String phone,
                          @RequestParam("code") String code,
                          @RequestParam("type") String type);

    /**
     * 发送邮箱验证码
     *
     * @param email 邮箱
     * @param type  验证码类型
     * @return 发送结果
     */
    @PostMapping("/send-email-code")
    Boolean sendEmailCode(@RequestParam("email") String email,
                          @RequestParam("type") String type);

    /**
     * 验证邮箱验证码
     *
     * @param email 邮箱
     * @param code  验证码
     * @param type  验证码类型
     * @return 验证结果
     */
    @PostMapping("/verify-email-code")
    Boolean verifyEmailCode(@RequestParam("email") String email,
                            @RequestParam("code") String code,
                            @RequestParam("type") String type);

    /**
     * 刷新访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新的访问令牌
     */
    @PostMapping("/refresh-token")
    String refreshToken(@RequestParam("refreshToken") String refreshToken);

    /**
     * 注销令牌
     *
     * @param token 访问令牌
     * @return 注销结果
     */
    @PostMapping("/logout")
    Boolean logout(@RequestParam("token") String token);
}
