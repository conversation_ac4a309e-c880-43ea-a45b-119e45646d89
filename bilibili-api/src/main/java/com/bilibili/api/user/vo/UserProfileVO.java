package com.bilibili.api.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户资料VO
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户资料信息")
public class UserProfileVO {

    @Schema(description = "用户ID", example = "123456")
    private Long uid;

    @Schema(description = "用户名", example = "user123")
    private String username;

    @Schema(description = "昵称", example = "小明")
    private String nickname;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    @Schema(description = "横幅图片URL", example = "https://example.com/banner.jpg")
    private String banner;

    @Schema(description = "性别", example = "1", allowableValues = {"0", "1", "2"})
    private Integer gender; // 0未知 1男 2女

    @Schema(description = "生日", example = "1990-01-01")
    private LocalDate birthday;

    @Schema(description = "个人签名", example = "这个人很懒，什么都没有留下")
    private String signature;

    @Schema(description = "位置", example = "北京市")
    private String location;

    @Schema(description = "用户等级", example = "5")
    private Integer level;

    @Schema(description = "经验值", example = "12500")
    private Integer exp;

    @Schema(description = "硬币数量", example = "1000.0")
    private Double coins;

    @Schema(description = "VIP类型", example = "2", allowableValues = {"0", "1", "2"})
    private Integer vipType; // 0普通 1月会员 2年会员

    @Schema(description = "VIP状态", example = "1", allowableValues = {"0", "1"})
    private Integer vipStatus; // 0无效 1有效

    @Schema(description = "VIP到期时间", example = "2024-12-31T23:59:59")
    private LocalDateTime vipDueDate;

    @Schema(description = "是否已认证", example = "true")
    private Boolean isVerified;

    @Schema(description = "认证类型", example = "1", allowableValues = {"0", "1", "2", "3"})
    private Integer verifiedType; // 0无认证 1个人认证 2企业认证 3机构认证

    @Schema(description = "认证信息", example = "知名UP主")
    private String verifiedInfo;

    @Schema(description = "学校", example = "清华大学")
    private String school;

    @Schema(description = "个人标签", example = "技术,音乐,旅行")
    private String personalTags;

    @Schema(description = "注册时间", example = "2023-01-01T12:00:00")
    private LocalDateTime registerTime;

    @Schema(description = "最后登录时间", example = "2024-01-01T12:00:00")
    private LocalDateTime lastLoginTime;

    @Schema(description = "是否关注", example = "false")
    private Boolean isFollowing;

    @Schema(description = "是否被关注", example = "false")
    private Boolean isFollowed;

    @Schema(description = "关注数", example = "100")
    private Integer followingCount;

    @Schema(description = "粉丝数", example = "1000")
    private Integer followerCount;

    @Schema(description = "视频数", example = "50")
    private Integer videoCount;

    @Schema(description = "获赞数", example = "10000")
    private Long likeCount;

    @Schema(description = "播放数", example = "100000")
    private Long viewCount;
}
