package com.bilibili.api.user;

import com.bilibili.common.response.ApiResponse;
import com.bilibili.common.dto.PageResult;
import com.bilibili.api.user.dto.UserUpdateDTO;
import com.bilibili.api.user.vo.UserProfileVO;
import com.bilibili.api.user.vo.UserStatsVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 用户服务API接口定义
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Tag(name = "用户管理", description = "用户信息管理相关接口")
@RequestMapping("/api/v1/user")
public interface UserApi {

    /**
     * 获取用户详细信息
     */
    @Operation(summary = "获取用户信息", description = "根据用户ID获取用户详细信息")
    @GetMapping("/{uid}")
    ApiResponse<UserProfileVO> getUserProfile(
            @Parameter(description = "用户ID", required = true) @PathVariable Long uid);

    /**
     * 获取当前用户信息
     */
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    @GetMapping("/me")
    ApiResponse<UserProfileVO> getCurrentUserProfile(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid);

    /**
     * 更新用户信息
     */
    @Operation(summary = "更新用户信息", description = "更新用户基本信息")
    @PutMapping("/profile")
    ApiResponse<Void> updateUserProfile(
            @Parameter(description = "用户ID", hidden = true) @RequestHeader("X-User-Id") Long uid,
            @Valid @RequestBody UserUpdateDTO updateDTO);

    /**
     * 获取用户统计信息
     */
    @Operation(summary = "获取用户统计", description = "获取用户的关注、粉丝、视频等统计信息")
    @GetMapping("/{uid}/stats")
    ApiResponse<UserStatsVO> getUserStats(
            @Parameter(description = "用户ID", required = true) @PathVariable Long uid);

    /**
     * 关注用户
     */
    @Operation(summary = "关注用户", description = "关注指定用户")
    @PostMapping("/{uid}/follow")
    ApiResponse<Void> followUser(
            @Parameter(description = "当前用户ID", hidden = true) @RequestHeader("X-User-Id") Long currentUid,
            @Parameter(description = "被关注用户ID", required = true) @PathVariable Long uid);

    /**
     * 取消关注
     */
    @Operation(summary = "取消关注", description = "取消关注指定用户")
    @DeleteMapping("/{uid}/follow")
    ApiResponse<Void> unfollowUser(
            @Parameter(description = "当前用户ID", hidden = true) @RequestHeader("X-User-Id") Long currentUid,
            @Parameter(description = "被取消关注用户ID", required = true) @PathVariable Long uid);

    /**
     * 获取关注列表
     */
    @Operation(summary = "获取关注列表", description = "获取用户的关注列表")
    @GetMapping("/{uid}/following")
    ApiResponse<PageResult<UserProfileVO>> getFollowingList(
            @Parameter(description = "用户ID", required = true) @PathVariable Long uid,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size);

    /**
     * 获取粉丝列表
     */
    @Operation(summary = "获取粉丝列表", description = "获取用户的粉丝列表")
    @GetMapping("/{uid}/followers")
    ApiResponse<PageResult<UserProfileVO>> getFollowersList(
            @Parameter(description = "用户ID", required = true) @PathVariable Long uid,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size);
}
