package com.bilibili.api.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 内容分析结果DTO
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@Accessors(chain = true)
public class ContentAnalysisDTO {

    /**
     * 内容类型 1视频 2用户 3评论
     */
    private Integer contentType;

    /**
     * 内容ID
     */
    private Long contentId;

    /**
     * 分析类型 1摘要 2标签 3情感 4质量评分
     */
    private Integer analysisType;

    /**
     * 分析结果
     */
    private Map<String, Object> analysisResult;

    /**
     * 置信度
     */
    private BigDecimal confidenceScore;

    /**
     * 使用的模型
     */
    private String modelUsed;

    /**
     * 分析版本
     */
    private String analysisVersion;

    /**
     * 摘要内容（当分析类型为摘要时）
     */
    private String summary;

    /**
     * 标签列表（当分析类型为标签时）
     */
    private List<String> tags;

    /**
     * 情感分析结果（当分析类型为情感时）
     */
    private SentimentResult sentiment;

    /**
     * 质量评分（当分析类型为质量评分时）
     */
    private QualityScore qualityScore;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 情感分析结果
     */
    @Data
    @Accessors(chain = true)
    public static class SentimentResult {
        /**
         * 情感类型：positive/negative/neutral
         */
        private String sentiment;

        /**
         * 置信度
         */
        private Double confidence;

        /**
         * 详细分数
         */
        private Map<String, Double> scores;
    }

    /**
     * 质量评分
     */
    @Data
    @Accessors(chain = true)
    public static class QualityScore {
        /**
         * 总分
         */
        private Double totalScore;

        /**
         * 内容质量分
         */
        private Double contentQuality;

        /**
         * 原创性分
         */
        private Double originality;

        /**
         * 受欢迎程度分
         */
        private Double popularity;

        /**
         * 详细评分
         */
        private Map<String, Double> detailScores;
    }
}
