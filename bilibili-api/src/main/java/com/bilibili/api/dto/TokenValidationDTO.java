package com.bilibili.api.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 令牌验证结果DTO
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@Accessors(chain = true)
public class TokenValidationDTO {

    /**
     * 是否有效
     */
    private Boolean valid;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 用户角色
     */
    private String roles;

    /**
     * 令牌过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 创建有效的验证结果
     */
    public static TokenValidationDTO valid(Long userId, String username, String roles, LocalDateTime expireTime) {
        return new TokenValidationDTO()
                .setValid(true)
                .setUserId(userId)
                .setUsername(username)
                .setRoles(roles)
                .setExpireTime(expireTime);
    }

    /**
     * 创建无效的验证结果
     */
    public static TokenValidationDTO invalid(String errorMessage) {
        return new TokenValidationDTO()
                .setValid(false)
                .setErrorMessage(errorMessage);
    }
}
