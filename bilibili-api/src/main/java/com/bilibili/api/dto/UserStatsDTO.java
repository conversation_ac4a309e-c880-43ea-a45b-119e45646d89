package com.bilibili.api.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 用户统计信息DTO
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@Accessors(chain = true)
public class UserStatsDTO {

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 关注数
     */
    private Integer followingCount;

    /**
     * 粉丝数
     */
    private Integer followerCount;

    /**
     * 视频数
     */
    private Integer videoCount;

    /**
     * 总播放数
     */
    private Long viewCount;

    /**
     * 总点赞数
     */
    private Long likeCount;

    /**
     * 总投币数
     */
    private Long coinCount;

    /**
     * 总收藏数
     */
    private Long favoriteCount;

    /**
     * 总分享数
     */
    private Long shareCount;

    /**
     * 总评论数
     */
    private Long commentCount;

    /**
     * 总弹幕数
     */
    private Long danmuCount;

    /**
     * 直播时长(秒)
     */
    private Long liveTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
