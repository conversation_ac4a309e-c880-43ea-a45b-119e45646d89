package com.bilibili.api.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户信息DTO
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@Accessors(chain = true)
public class UserInfoDTO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户UID
     */
    private Long uid;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 横幅URL
     */
    private String banner;

    /**
     * 性别 0未知 1男 2女
     */
    private Integer gender;

    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 个人签名
     */
    private String signature;

    /**
     * 位置
     */
    private String location;

    /**
     * 用户等级
     */
    private Integer level;

    /**
     * 经验值
     */
    private Integer exp;

    /**
     * 硬币数量
     */
    private Double coins;

    /**
     * VIP类型 0无 1月度会员 2年度会员 3终身会员
     */
    private Integer vipType;

    /**
     * VIP状态 0非VIP 1VIP
     */
    private Integer vipStatus;

    /**
     * VIP到期时间
     */
    private LocalDateTime vipDueDate;

    /**
     * 用户状态 0正常 1封禁 2注销
     */
    private Integer status;

    /**
     * 是否认证 0未认证 1已认证
     */
    private Boolean isVerified;

    /**
     * 认证类型 0无 1个人 2机构
     */
    private Integer verifiedType;

    /**
     * 认证信息
     */
    private String verifiedInfo;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 手机号是否已绑定
     */
    private Boolean phoneBound;

    /**
     * 微信OpenID
     */
    private String wechatOpenId;

    /**
     * 微信UnionID
     */
    private String wechatUnionId;

    /**
     * 学校信息
     */
    private String school;

    /**
     * 个人标签
     */
    private String personalTags;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 实名认证状态 0未认证 1已认证
     */
    private Integer realNameVerified;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
