package com.bilibili.api.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * AI聊天响应DTO
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@Accessors(chain = true)
public class AiChatResponseDTO {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 消息ID
     */
    private Long messageId;

    /**
     * AI回复内容
     */
    private String content;

    /**
     * 使用的模型
     */
    private String modelUsed;

    /**
     * 输入Token数
     */
    private Integer promptTokens;

    /**
     * 输出Token数
     */
    private Integer completionTokens;

    /**
     * 总Token数
     */
    private Integer totalTokens;

    /**
     * 成本（美元）
     */
    private Double cost;

    /**
     * 响应时间（毫秒）
     */
    private Integer responseTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 是否成功
     */
    private Boolean success = true;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 创建成功响应
     */
    public static AiChatResponseDTO success(String sessionId, String content) {
        return new AiChatResponseDTO()
                .setSessionId(sessionId)
                .setContent(content)
                .setSuccess(true)
                .setCreatedAt(LocalDateTime.now());
    }

    /**
     * 创建失败响应
     */
    public static AiChatResponseDTO error(String sessionId, String errorMessage) {
        return new AiChatResponseDTO()
                .setSessionId(sessionId)
                .setSuccess(false)
                .setErrorMessage(errorMessage)
                .setCreatedAt(LocalDateTime.now());
    }
}
