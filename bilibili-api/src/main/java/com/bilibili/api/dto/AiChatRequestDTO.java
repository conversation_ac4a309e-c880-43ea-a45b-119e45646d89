package com.bilibili.api.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * AI聊天请求DTO
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@Accessors(chain = true)
public class AiChatRequestDTO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户消息
     */
    private String message;

    /**
     * 对话类型 1问答 2分析 3推荐
     */
    private Integer conversationType;

    /**
     * 历史消息（可选）
     */
    private List<ChatMessage> history;

    /**
     * 是否流式响应
     */
    private Boolean stream = false;

    /**
     * 最大Token数
     */
    private Integer maxTokens;

    /**
     * 温度参数
     */
    private Double temperature;

    /**
     * 聊天消息
     */
    @Data
    @Accessors(chain = true)
    public static class ChatMessage {
        /**
         * 消息角色 user/assistant/system
         */
        private String role;

        /**
         * 消息内容
         */
        private String content;
    }
}
