# Bilibili API 模块

## 📁 项目结构

本模块采用按功能模块组织的包结构，每个功能模块包含独立的 client、dto、vo、api 子包：

```
com.bilibili.api/
├── user/                           # 用户模块
│   ├── client/
│   │   └── UserServiceClient.java  # 用户服务Feign客户端
│   ├── dto/
│   │   ├── UserInfoDTO.java        # 用户信息传输对象
│   │   ├── UserUpdateDTO.java      # 用户更新请求
│   │   ├── UserStatsDTO.java       # 用户统计数据
│   │   └── UserSearchDTO.java      # 用户搜索条件
│   ├── vo/
│   │   ├── UserProfileVO.java      # 用户资料视图对象
│   │   └── UserStatsVO.java        # 用户统计视图对象
│   └── api/
│       └── UserApi.java            # 用户API接口定义
├── auth/                           # 认证模块
│   ├── client/
│   │   └── AuthServiceClient.java  # 认证服务Feign客户端
│   ├── dto/
│   │   ├── LoginRequestDTO.java    # 登录请求
│   │   ├── LoginResponseDTO.java   # 登录响应
│   │   ├── RegisterRequestDTO.java # 注册请求
│   │   ├── TokenValidationDTO.java # 令牌验证
│   │   └── SendCodeRequestDTO.java # 发送验证码请求
│   ├── vo/
│   │   ├── LoginResultVO.java      # 登录结果视图
│   │   └── UserTokenVO.java        # 用户令牌视图
│   └── api/
│       └── AuthApi.java            # 认证API接口定义
├── ai/                             # AI模块
│   ├── client/
│   │   └── AiServiceClient.java    # AI服务Feign客户端
│   ├── dto/
│   │   ├── AiChatRequestDTO.java   # AI聊天请求
│   │   ├── AiChatResponseDTO.java  # AI聊天响应
│   │   ├── ContentAnalysisDTO.java # 内容分析
│   │   └── RecommendationDTO.java  # 推荐数据
│   ├── vo/
│   │   ├── ChatSessionVO.java      # 聊天会话视图
│   │   └── AnalysisResultVO.java   # 分析结果视图
│   └── api/
│       └── AiApi.java              # AI API接口定义
├── video/                          # 视频模块（待实现）
│   ├── client/
│   ├── dto/
│   ├── vo/
│   └── api/
├── comment/                        # 评论模块（待实现）
│   ├── client/
│   ├── dto/
│   ├── vo/
│   └── api/
└── common/                         # 通用模块
    ├── dto/
    │   ├── PageRequestDTO.java     # 分页请求
    │   └── PageResponseDTO.java    # 分页响应
    └── vo/
        └── BaseVO.java             # 基础视图对象
```

## 🎯 设计原则

### 1. 模块化设计
- **按功能分包**：每个业务功能独立成包，便于维护和扩展
- **职责清晰**：client、dto、vo、api 各司其职
- **低耦合**：模块间通过接口交互，减少直接依赖

### 2. 包结构说明

#### client/ - Feign客户端
- 定义服务间调用接口
- 使用 `@FeignClient` 注解
- 返回统一的 `ApiResponse<T>` 格式

#### dto/ - 数据传输对象
- 用于服务间数据传输
- 包含完整的验证注解
- 支持序列化/反序列化

#### vo/ - 视图对象
- 面向前端的数据展示
- 可能包含计算字段
- 优化的数据结构

#### api/ - API接口定义
- 定义REST API规范
- 包含完整的Swagger文档
- 统一的参数和返回格式

### 3. 命名规范

#### 类命名
- **DTO**: `XxxDTO.java` - 数据传输对象
- **VO**: `XxxVO.java` - 视图对象  
- **Client**: `XxxServiceClient.java` - Feign客户端
- **Api**: `XxxApi.java` - API接口定义

#### 包命名
- 使用小写字母
- 按业务功能命名：user、auth、ai、video、comment等
- 子包固定：client、dto、vo、api

## 🔧 使用示例

### 1. Feign客户端调用
```java
@Autowired
private UserServiceClient userServiceClient;

public UserInfoDTO getUserInfo(Long userId) {
    ApiResponse<UserInfoDTO> response = userServiceClient.getUserInfo(userId);
    return response.getData();
}
```

### 2. API接口实现
```java
@RestController
@RequestMapping("/api/v1/user")
public class UserController implements UserApi {
    
    @Override
    public ApiResponse<UserInfoDTO> getUserInfo(Long userId) {
        // 实现逻辑
        return ApiResponse.success(userInfo);
    }
}
```

### 3. 分页查询
```java
PageRequestDTO pageRequest = new PageRequestDTO();
pageRequest.setPage(1);
pageRequest.setSize(20);

ApiResponse<PageResponseDTO<UserInfoDTO>> response = 
    userServiceClient.searchUsers(searchDTO);
```

## 📝 开发指南

### 1. 添加新模块
1. 在 `com.bilibili.api` 下创建新的功能包
2. 按照标准结构创建 client、dto、vo、api 子包
3. 实现对应的接口和数据对象

### 2. 扩展现有模块
1. 在对应的子包中添加新的类
2. 更新相关的Client接口
3. 保持向后兼容性

### 3. 版本管理
- API接口支持版本控制
- 使用 `@RequestMapping("/api/v1/...")` 指定版本
- 新版本向后兼容旧版本

## 🚀 优势

1. **清晰的结构**：按功能模块组织，易于理解和维护
2. **高内聚低耦合**：每个模块职责单一，模块间松耦合
3. **易于扩展**：新增功能只需添加对应模块
4. **统一规范**：所有模块遵循相同的结构和命名规范
5. **便于测试**：模块化设计便于单元测试和集成测试

## 📋 待完成

- [ ] 完善 video 模块
- [ ] 完善 comment 模块  
- [ ] 添加 live 直播模块
- [ ] 添加 message 消息模块
- [ ] 完善 common 通用组件
