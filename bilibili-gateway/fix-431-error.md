# 🔧 修复431请求头过大错误

## 🐛 问题分析

**HTTP 431 Request Header Fields Too Large** 错误表示：
- 请求头字段总大小超过了服务器限制
- 可能是网关过滤器添加了过多请求头
- 或者单个请求头值过大

## ✅ 已修复内容

### 1. 增加服务器请求头大小限制
```yaml
server:
  port: 8080
  tomcat:
    uri-encoding: UTF-8
    max-http-header-size: 65536  # 64KB
    max-http-post-size: 2097152  # 2MB
  netty:
    max-initial-line-length: 8192  # 8KB
    max-header-size: 65536         # 64KB
    max-chunk-size: 8192           # 8KB
```

### 2. 创建Netty服务器配置类
新增 `NettyServerConfig.java`：
- 自定义Netty HTTP解码器配置
- 增加请求头大小限制到64KB
- 禁用严格的请求头验证

### 3. 简化JWT过滤器请求头
```java
// 修复前：添加多个请求头
.header("X-User-Id", uid.toString())
.header("X-Username", username)
.header("X-User-Roles", "ROLE_USER")
.header("X-Request-Source", "gateway")

// 修复后：只添加必要的请求头
.header("X-User-Id", uid.toString())
.header("X-Username", username)
```

### 4. 简化全局过滤器
```yaml
# 修复前：多个全局过滤器
default-filters:
  - AddRequestHeader=X-Request-Source,gateway
  - AddRequestHeader=X-Gateway-Version,v1.0

# 修复后：简化过滤器
default-filters:
  - AddRequestHeader=X-Gateway,bilibili
```

### 5. 优化文档路由
移除了不必要的请求头过滤器，简化路由配置。

## 🚀 测试步骤

### 1. 重启网关服务
```bash
# 停止当前网关服务 (Ctrl+C)
cd bilibili-gateway
mvn spring-boot:run
```

### 2. 验证网关启动
查看启动日志，确认没有配置错误：
```
INFO  - Netty started on port 8080
INFO  - Started BilibiliGatewayApplication
```

### 3. 测试文档访问
访问：**http://localhost:8080/doc.html**

**预期结果**：
- ✅ 不再出现431错误
- ✅ 能正常显示Knife4j文档页面
- ✅ 网关日志显示200状态码

### 4. 查看网关日志
应该看到类似日志：
```
INFO  - 网关处理请求: GET /doc.html
INFO  - 路径 /doc.html 在白名单中，跳过JWT认证
INFO  - ✅ [ID] 请求完成 - GET /doc.html | 状态: ✅200 | 耗时: XXms
```

## 🧪 完整测试流程

### 步骤1：验证基础访问
1. 访问 http://localhost:8080/doc.html
2. 确认返回200状态码
3. 验证页面正常显示

### 步骤2：测试API文档功能
1. 检查是否能看到认证服务接口
2. 检查是否能看到用户服务接口
3. 验证接口文档完整性

### 步骤3：测试认证流程
1. 使用 `POST /api/v1/auth/login-by-password` 登录
2. 获取JWT token
3. 配置认证：`Bearer your-token`

### 步骤4：测试用户接口
1. 调用 `GET /api/v1/user/me`
2. 验证返回用户信息
3. 确认不再出现"用户未认证"错误

## 🔍 故障排查

### 如果仍然出现431错误

1. **检查浏览器请求头**
   - 打开浏览器开发者工具
   - 查看Network标签页
   - 检查请求头大小

2. **增加更大的限制**
   ```yaml
   server:
     netty:
       max-header-size: 131072  # 128KB
   ```

3. **检查是否有循环重定向**
   - 查看网关日志
   - 确认路由配置正确

### 如果出现其他错误

1. **404错误**：检查路由配置和服务注册
2. **500错误**：查看详细错误日志
3. **超时错误**：检查服务间网络连接

## 📊 配置对比

### 修复前的问题
- 默认请求头限制过小（通常8KB）
- 过滤器添加过多请求头
- 没有针对性的Netty配置

### 修复后的改进
- 请求头限制增加到64KB
- 简化了不必要的请求头
- 专门的Netty服务器配置
- 优化的路由过滤器

## 🎯 预期结果

修复完成后，您应该能够：

1. ✅ 正常访问 http://localhost:8080/doc.html
2. ✅ 看到完整的API文档界面
3. ✅ 网关日志显示200状态码
4. ✅ 不再出现431请求头过大错误
5. ✅ 能够正常进行JWT认证
6. ✅ 用户接口正常工作

## 📝 技术说明

**为什么会出现431错误？**
- Spring Cloud Gateway基于Netty
- Netty默认请求头限制较小
- 网关过滤器会添加额外请求头
- 累积后可能超过限制

**解决方案原理**：
- 增加Netty HTTP解码器的请求头限制
- 减少不必要的请求头添加
- 优化过滤器配置

请重启网关服务后测试，应该就能解决431错误了！
