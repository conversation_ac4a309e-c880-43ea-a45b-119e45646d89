package com.bilibili.gateway;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Bilibili网关启动类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@EnableDiscoveryClient
@SpringBootApplication
public class GatewayApplication {

    public static void main(String[] args) {
        System.setProperty("spring.application.name", "bilibili-gateway");

        // 设置Netty系统属性，解决请求头过大问题
        System.setProperty("reactor.netty.http.server.maxInitialLineLength", "32768");
        System.setProperty("reactor.netty.http.server.maxHeaderSize", "131072");

        ConfigurableApplicationContext context = SpringApplication.run(GatewayApplication.class, args);
        Environment env = context.getEnvironment();

        // 延迟输出启动信息，确保所有组件都已初始化
        ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor();
        executor.schedule(() -> {
            try {
                printSuccessInfo();
            } catch (Exception e) {
                log.error("输出启动信息时发生错误", e);
            } finally {
                executor.shutdown();
            }
        }, 2, TimeUnit.SECONDS);
    }

    private static void printSuccessInfo() {
        System.out.println();
        System.out.println("\033[32m           🎉 网关服务启动成功！ 🎉\033[0m");
        System.out.println("========================================");
        System.out.println("🔧 网关地址: \033[1;32mhttp://localhost:8080\033[0m");
        System.out.println("📖 聚合文档: \033[1;32mhttp://localhost:8080/doc.html\033[0m");
        System.out.println("🌟 健康检查: \033[1;32mhttp://localhost:8080/actuator/health\033[0m");
        System.out.println("🛣️ 路由信息: \033[1;32mhttp://localhost:8080/actuator/gateway/routes\033[0m");
        System.out.println("🌐 Nacos控制台: \033[1;32mhttp://***********:8848/nacos\033[0m");
        System.out.println("========================================");
        System.out.println("\033[1;36m🔗 后端服务地址:\033[0m");
        System.out.println("   🔐 认证服务: \033[1;33mhttp://localhost:8090\033[0m");
        System.out.println("   👤 用户服务: \033[1;33mhttp://localhost:8091\033[0m");
        System.out.println("========================================");
        System.out.println("\033[1;36m🎯 环境: 生产环境 (连接远程Nacos)\033[0m");
        System.out.println("\033[1;33m⚡ 服务: bilibili-gateway-service\033[0m");
        System.out.println("\033[1;35m🔥 版本: v1.0.0\033[0m");
        System.out.println("========================================");
    }


}
