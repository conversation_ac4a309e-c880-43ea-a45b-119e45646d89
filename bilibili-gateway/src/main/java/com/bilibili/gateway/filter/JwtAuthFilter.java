package com.bilibili.gateway.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT认证全局过滤器
 * 
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Component
public class JwtAuthFilter implements GlobalFilter, Ordered {

    @Value("${app.jwt.secret}")
    private String jwtSecret;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 不需要认证的路径
    private static final String[] EXCLUDE_PATHS = {
        "/api/v1/auth/login-by-password",
        "/api/v1/auth/login-by-code",
        "/api/v1/auth/send-code",
        "/api/v1/auth/refresh-token",
        "/api/v1/auth/logout",
        "/api/v1/auth/register",
        "/api/v1/auth/wechat",
        "/api/v1/auth/health",
        "/api/v1/user/test",
        "/actuator",
        "/doc.html",
        "/api/v1/doc.html",
        "/docs",
        "/swagger-ui",
        "/v3/api-docs",
        "/webjars",
        "/favicon.ico"
    };

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();

        log.debug("JWT认证过滤器: path={}", path);

        // 检查是否需要认证
        if (isExcludePath(path)) {
            log.debug("跳过JWT认证: path={}", path);
            return chain.filter(exchange);
        }

        // 获取Authorization头
        String authHeader = request.getHeaders().getFirst("Authorization");
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            log.warn("JWT认证失败: 缺少Authorization头");
            return unauthorizedResponse(exchange, "缺少认证信息");
        }

        try {
            // 提取JWT token
            String token = authHeader.substring(7);
            
            // 验证JWT
            Claims claims = parseJWT(token);
            
            // 提取用户信息
            Long uid = claims.get("uid", Long.class);
            String username = claims.get("username", String.class);
            Map<String, Object> vipInfo = (Map<String, Object>) claims.get("vip");

            if (uid == null || username == null) {
                log.warn("JWT认证失败: token中缺少用户信息");
                return unauthorizedResponse(exchange, "认证信息无效");
            }

            // 构建新的请求，添加用户信息到请求头
            ServerHttpRequest.Builder requestBuilder = request.mutate()
                    .header("X-User-Id", uid.toString())
                    .header("X-Username", username);

            // 添加VIP信息到请求头
            if (vipInfo != null) {
                String vipInfoJson = objectMapper.writeValueAsString(vipInfo);
                requestBuilder.header("X-Vip-Info", vipInfoJson);
                
                // 添加简化的VIP状态头（方便其他服务使用）
                Boolean isVip = (Boolean) vipInfo.get("isVip");
                Integer vipType = (Integer) vipInfo.get("vipType");
                requestBuilder.header("X-Is-Vip", isVip != null ? isVip.toString() : "false")
                             .header("X-Vip-Type", vipType != null ? vipType.toString() : "0");
            } else {
                // 默认非VIP信息
                requestBuilder.header("X-Vip-Info", "{\"isVip\":false,\"vipType\":0}")
                             .header("X-Is-Vip", "false")
                             .header("X-Vip-Type", "0");
            }

            ServerHttpRequest newRequest = requestBuilder.build();
            ServerWebExchange newExchange = exchange.mutate().request(newRequest).build();

            log.debug("JWT认证成功: uid={}, username={}, isVip={}", 
                uid, username, vipInfo != null ? vipInfo.get("isVip") : false);

            return chain.filter(newExchange);

        } catch (Exception e) {
            log.warn("JWT认证失败: {}", e.getMessage());
            return unauthorizedResponse(exchange, "认证信息无效");
        }
    }

    private boolean isExcludePath(String path) {
        for (String excludePath : EXCLUDE_PATHS) {
            if (path.startsWith(excludePath)) {
                return true;
            }
        }
        return false;
    }

    private Claims parseJWT(String token) {
        SecretKey key = Keys.hmacShaKeyFor(jwtSecret.getBytes(StandardCharsets.UTF_8));
        return Jwts.parser()
                .verifyWith(key)
                .build()
                .parseSignedClaims(token)
                .getPayload();
    }

    private Mono<Void> unauthorizedResponse(ServerWebExchange exchange, String message) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);

        Map<String, Object> result = new HashMap<>();
        result.put("code", 401);
        result.put("message", message);
        result.put("data", null);
        result.put("timestamp", System.currentTimeMillis());

        try {
            String body = objectMapper.writeValueAsString(result);
            DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
            return response.writeWith(Mono.just(buffer));
        } catch (Exception e) {
            log.error("写入响应失败", e);
            return response.setComplete();
        }
    }

    @Override
    public int getOrder() {
        return -100; // 高优先级，在其他过滤器之前执行
    }
}
