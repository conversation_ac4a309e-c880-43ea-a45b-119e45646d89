package com.bilibili.gateway.filter;

import com.bilibili.gateway.util.GatewayJwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;

/**
 * 网关JWT认证全局过滤器
 * 统一处理JWT认证，验证成功后将用户信息添加到请求头中转发给微服务
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationGlobalFilter implements GlobalFilter, Ordered {

    private final GatewayJwtUtil jwtUtil;

    /**
     * 白名单路径，这些路径不需要JWT认证
     */
    private static final List<String> WHITELIST_PATHS = Arrays.asList(
        "/api/v1/auth/login-by-password",
        "/api/v1/auth/login-by-code",
        "/api/v1/auth/send-code",
        "/api/v1/auth/refresh-token",
        "/api/v1/auth/logout",
        "/api/v1/auth/register",
        "/api/v1/auth/wechat",
        "/api/v1/auth/sms/send",
        "/api/v1/auth/email/send",
        "/api/v1/auth/qr/generate",
        "/api/v1/auth/qr/login",
        "/api/v1/auth/password/reset",
        "/api/v1/auth/health",
        "/api/v1/auth/test",
        "/api/v1/user/test",
        "/api/v1/user/test/health",
        "/api/v1/user/test/context",
        "/actuator",
        // Swagger/Knife4j 相关路径
        "/doc.html",
        "/api/v1/doc.html",
        "/docs",
        "/swagger-ui",
        "/v3/api-docs",
        "/webjars",
        "/favicon.ico"
    );

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();
        String method = request.getMethod().name();

        log.info("网关处理请求: {} {}", method, path);

        // 对于OPTIONS预检请求，直接放行，不进行JWT认证
        if ("OPTIONS".equals(method)) {
            log.info("OPTIONS预检请求，直接放行: {}", path);
            return chain.filter(exchange);
        }

        // 检查是否在白名单中
        if (isWhitelistPath(path)) {
            log.info("路径 {} 在白名单中，跳过JWT认证", path);
            return chain.filter(exchange);
        }

        log.info("路径 {} 不在白名单中，需要JWT认证", path);

        // 从请求头中获取Authorization
        String authHeader = request.getHeaders().getFirst("Authorization");
        String token = jwtUtil.extractTokenFromAuthHeader(authHeader);

        if (!StringUtils.hasText(token)) {
            log.warn("请求路径 {} 缺少JWT token", path);
            return handleUnauthorized(exchange, "缺少认证token");
        }

        // 验证JWT token
        if (!jwtUtil.validateToken(token)) {
            log.warn("请求路径 {} 的JWT token无效", path);
            return handleUnauthorized(exchange, "无效的认证token");
        }

        // 提取用户信息
        Long uid = jwtUtil.getUidFromToken(token);
        String username = jwtUtil.getUsernameFromToken(token);

        if (uid == null || username == null) {
            log.warn("无法从token中提取用户信息");
            return handleUnauthorized(exchange, "无效的用户信息");
        }

        // 创建新的请求，添加用户信息到请求头
        ServerHttpRequest modifiedRequest = request.mutate()
            .header("X-User-Id", uid.toString())
            .header("X-Username", username)
            .header("X-User-Roles", "ROLE_USER") // 可以根据实际需求从token中提取角色信息
            .header("X-Request-Source", "gateway")
            .build();

        log.debug("JWT认证成功，用户: {} ({}), 转发到微服务", username, uid);

        // 继续过滤器链，使用修改后的请求
        return chain.filter(exchange.mutate().request(modifiedRequest).build());
    }

    /**
     * 检查路径是否在白名单中
     */
    private boolean isWhitelistPath(String path) {
        return WHITELIST_PATHS.stream().anyMatch(path::startsWith);
    }

    /**
     * 处理未授权请求
     */
    private Mono<Void> handleUnauthorized(ServerWebExchange exchange, String message) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");

        String body = String.format("{\"code\":401,\"message\":\"%s\",\"data\":null,\"timestamp\":\"%s\"}",
            message, java.time.Instant.now());

        org.springframework.core.io.buffer.DataBuffer buffer = response.bufferFactory().wrap(body.getBytes());
        return response.writeWith(reactor.core.publisher.Mono.just(buffer));
    }

    @Override
    public int getOrder() {
        // 设置较高的优先级，确保在其他过滤器之前执行
        return -100;
    }
}
