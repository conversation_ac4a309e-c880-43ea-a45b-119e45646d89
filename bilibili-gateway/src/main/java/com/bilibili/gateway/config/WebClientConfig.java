package com.bilibili.gateway.config;

import io.netty.channel.ChannelOption;
import io.netty.handler.codec.http.HttpClientCodec;
import org.springframework.cloud.gateway.config.HttpClientCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.netty.http.client.HttpClient;

/**
 * WebClient配置
 * 解决网关作为客户端时的请求头过大问题
 * 
 * <AUTHOR>
 * @since 2025-07-30
 */
@Configuration
public class WebClientConfig {

    /**
     * 自定义HttpClient配置
     * 解决网关转发请求时的请求头大小限制
     */
    @Bean
    public HttpClientCustomizer httpClientCustomizer() {
        return httpClient -> httpClient
            .option(ChannelOption.SO_KEEPALIVE, true)
            .option(ChannelOption.TCP_NODELAY, true)
            .doOnConnected(connection -> {
                // 配置HTTP编解码器，增加请求头大小限制
                connection.addHandlerLast(new HttpClientCodec(
                    32768,   // maxInitialLineLength: 32KB
                    262144,  // maxHeaderSize: 256KB
                    16384    // maxChunkSize: 16KB
                ));
            });
    }
}
