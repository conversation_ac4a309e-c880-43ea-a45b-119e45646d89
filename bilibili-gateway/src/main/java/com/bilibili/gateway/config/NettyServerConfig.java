package com.bilibili.gateway.config;

import org.springframework.boot.web.embedded.netty.NettyReactiveWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.netty.http.server.HttpServer;

/**
 * Netty服务器配置
 * 解决请求头过大问题
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Configuration
public class NettyServerConfig {

    /**
     * 自定义Netty服务器配置
     * 使用正确的方式配置Spring Cloud Gateway的Netty服务器
     */
    @Bean
    public WebServerFactoryCustomizer<NettyReactiveWebServerFactory> nettyCustomizer() {
        return factory -> {
            factory.addServerCustomizers(this::customizeHttpServer);
        };
    }

    /**
     * 自定义HttpServer配置
     */
    private HttpServer customizeHttpServer(HttpServer httpServer) {
        return httpServer.httpRequestDecoder(spec -> {
            // 设置更大的请求头限制
            spec.maxInitialLineLength(32768);  // 32KB
            spec.maxHeaderSize(131072);        // 128KB
            spec.validateHeaders(false);       // 禁用严格验证
            return spec;
        });
    }
}
