package com.bilibili.gateway.config;

import org.springframework.boot.web.embedded.netty.NettyReactiveWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Netty服务器配置
 * 解决请求头过大问题
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Configuration
public class NettyServerConfig {

    /**
     * 自定义Netty服务器配置
     * 增加请求头大小限制，解决431错误
     */
    @Bean
    public WebServerFactoryCustomizer<NettyReactiveWebServerFactory> nettyCustomizer() {
        return factory -> factory.addServerCustomizers(httpServer ->
            httpServer.httpRequestDecoder(spec -> spec
                .maxInitialLineLength(16384)     // 16KB - 增加初始行长度
                .maxHeaderSize(65536)            // 64KB - 增加请求头大小限制
                .validateHeaders(false)          // 禁用严格的请求头验证
            )
        );
    }
}
