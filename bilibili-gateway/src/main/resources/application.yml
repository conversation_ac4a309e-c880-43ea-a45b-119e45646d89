# Bilibili网关服务主配置文件

# 基础配置
server:
  port: 8080  #端口
  # Netty 配置（Spring Cloud Gateway 使用）
  netty:
    max-http-header-size: 64KB  # 增加请求头大小限制，默认8KB

# 应用配置
spring:
  application:
    name: gateway-service
  profiles:
    active: prod

  # 强制指定Redis配置 - 覆盖任何默认配置
  data:
    redis:
      host: ***********
      port: 6379
      password: "@Xc123456"
      database: 0
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 1
          max-wait: 300ms


  # Jackson JSON配置
  jackson:
    property-naming-strategy: SNAKE_CASE
    default-property-inclusion: non_null
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai

  cloud:
    nacos:
      # Nacos Server地址
      server-addr: ***********:8848
      # Nacos配置
      config:
        namespace: public
        group: DEFAULT_GROUP
        file-extension: yml
        # 重要：添加超时和重试配置，避免启动卡死
        timeout: 3000
        max-retry: 3
        # 如果配置中心连接失败，允许应用继续启动
        fail-fast: false
      # Nacos服务发现
      discovery:
        namespace: public
        group: DEFAULT_GROUP
        enabled: true
    gateway:
      # HTTP服务器配置
      httpserver:
        max-http-header-size: 64KB  # 增加请求头大小限制，默认8KB

      # 全局过滤器配置（简化以避免请求头过大）
      default-filters:
        - AddRequestHeader=X-Gateway,bilibili

      routes: # 路由配置
        # ==================== 认证服务路由 ====================
        - id: auth-service
          uri: lb://auth-service
          predicates:
            - Path=/api/v1/auth/**
          filters:
            - StripPrefix=2  # 去掉 /api/v1
            - name: RequestRateLimiter  # 限流
              args:
                redis-rate-limiter.replenishRate: 100  # 每秒允许100个请求
                redis-rate-limiter.burstCapacity: 200  # 突发容量200
                redis-rate-limiter.requestedTokens: 1
            - name: Retry  # 重试
              args:
                retries: 3
                statuses: BAD_GATEWAY,GATEWAY_TIMEOUT
                methods: GET,POST
          metadata:
            description: "认证服务 - 登录、注册、JWT管理"
            version: "v1.0"

        # ==================== 用户服务路由 ====================
        - id: user-service
          uri: lb://user-service
          predicates:
            - Path=/api/v1/user/**
          filters:
            - StripPrefix=2  # 去掉 /api/v1
            - name: RequestRateLimiter  # 限流
              args:
                redis-rate-limiter.replenishRate: 200  # 用户服务请求更频繁
                redis-rate-limiter.burstCapacity: 400
                redis-rate-limiter.requestedTokens: 1
            - name: Retry
              args:
                retries: 3
                statuses: BAD_GATEWAY,GATEWAY_TIMEOUT
                methods: GET,POST,PUT
          metadata:
            description: "用户服务 - 个人中心、VIP、硬币管理"
            version: "v1.0"

        # ==================== VIP专享用户服务路由 ====================
        # 暂时注释掉VIP路由，因为VipAuthGatewayFilterFactory还未实现
        # - id: user-vip-service
        #   uri: lb://user-service
        #   predicates:
        #     - Path=/api/v1/user/vip/**
        #   filters:
        #     - StripPrefix=2  # 去掉 /api/v1
        #     - name: VipAuth  # VIP权限验证
        #       args:
        #         requiredLevel: 1
        #         message: "该功能需要开通大会员"
        #     - name: RequestRateLimiter
        #       args:
        #         redis-rate-limiter.replenishRate: 50
        #         redis-rate-limiter.burstCapacity: 100
        #         redis-rate-limiter.requestedTokens: 1
        #   metadata:
        #     description: "VIP专享功能"
        #     requiresVip: true

        # ==================== AI助手服务路由 ====================
        - id: ai-service
          uri: lb://aiassistant-service
          predicates:
            - Path=/api/v1/ai/**
          filters:
            - StripPrefix=2  # 去掉 /api/v1
            - name: RequestRateLimiter  # AI服务需要更严格的限流
              args:
                redis-rate-limiter.replenishRate: 30  # AI请求较消耗资源
                redis-rate-limiter.burstCapacity: 60
                redis-rate-limiter.requestedTokens: 1
            - name: Retry
              args:
                retries: 2  # AI服务重试次数少一些
                statuses: BAD_GATEWAY,GATEWAY_TIMEOUT
                methods: GET,POST
            - AddRequestHeader=X-AI-Version,v1.0
          metadata:
            description: "AI助手服务 - 智能问答、内容生成"
            version: "v1.0"

        # ==================== VIP专享AI服务路由 ====================
        # 暂时注释掉VIP AI路由，因为VipAuthGatewayFilterFactory还未实现
        # - id: ai-vip-service
        #   uri: lb://aiassistant-service
        #   predicates:
        #     - Path=/api/v1/ai/premium/**
        #   filters:
        #     - StripPrefix=2  # 去掉 /api/v1
        #     - name: VipAuth  # VIP权限验证
        #       args:
        #         requiredLevel: 2  # 年会员专享
        #         requiredPrivilege: "exclusive_content"
        #         message: "该AI功能需要年会员权限"
        #     - name: RequestRateLimiter
        #       args:
        #         redis-rate-limiter.replenishRate: 10  # VIP AI功能限流更严格
        #         redis-rate-limiter.burstCapacity: 20
        #         redis-rate-limiter.requestedTokens: 1
        #   metadata:
        #     description: "VIP专享AI功能"
        #     requiresVip: true
        #     vipLevel: 2

        # ==================== 文档路由 ====================
        - id: auth-docs
          uri: lb://auth-service
          predicates:
            - Path=/docs/auth/**
          filters:
            - StripPrefix=2
            - RewritePath=/docs/auth/(?<segment>.*), /$\{segment}
          metadata:
            description: "认证服务文档"
            public: true

        - id: user-docs
          uri: lb://user-service
          predicates:
            - Path=/docs/user/**
          filters:
            - StripPrefix=2
            - RewritePath=/docs/user/(?<segment>.*), /$\{segment}
          metadata:
            description: "用户服务文档"
            public: true

        - id: ai-docs
          uri: lb://aiassistant-service
          predicates:
            - Path=/docs/ai/**
          filters:
            - StripPrefix=2
            - RewritePath=/docs/ai/(?<segment>.*), /$\{segment}
          metadata:
            description: "AI服务文档"
            public: true

        # ==================== 聚合文档路由 ====================
        - id: gateway-docs
          uri: http://localhost:8080
          predicates:
            - Path=/doc.html,/swagger-ui/**,/v3/api-docs/**,/webjars/**
          metadata:
            description: "网关聚合文档"
            public: true
      globalcors: # 全局的跨域处理
        add-to-simple-url-handler-mapping: true # 解决options请求被拦截问题
        cors-configurations:
          '[/**]':
            allowedOriginPatterns: # 允许哪些网站的跨域请求
              - "*"
            allowedOrigins: # 明确允许的源, 增强安全性
              - "http://localhost:8080"
              - "http://localhost:8081"
              - "http://localhost:8091"
              - "http://localhost:8092"
              - "http://localhost:8093"
              - "http://127.0.0.1:8080"
              - "http://127.0.0.1:8081"
              - "http://127.0.0.1:8091"
              - "http://127.0.0.1:8092"
              - "http://127.0.0.1:8093"
            allowedMethods: # 允许的跨域ajax的请求方式
              - "GET"
              - "POST"
              - "DELETE"
              - "PUT"
              - "OPTIONS"
              - "HEAD"
              - "PATCH"
            allowedHeaders: # 允许在请求中携带的头信息
              - "*"
            allowCredentials: true # 是否允许携带cookie
            maxAge: 3600 # 这次跨域检测的有效期（秒）
            exposedHeaders: # 暴露给前端的响应头
              - "Authorization"
              - "Content-Type"
              - "Access-Control-Allow-Origin"
              - "Access-Control-Allow-Headers"
              - "*"

# 启用管理端点
management:
  endpoints:
    web:
      exposure:
        include: health,info,gateway,routes
  endpoint:
    health:
      show-details: always
    gateway:
      enabled: true

# JWT配置 - 与认证服务保持一致
app:
  jwt:
    secret: bilibiliAuthSecretKey2024SunsetTeamVeryLongSecretKeyForJWTTokenHS512
    access-token-expiration: 86400000  # 24小时
    refresh-token-expiration: 604800000 # 7天
    issuer: bilibili-auth
    audience: bilibili-users
  gateway:
    name: "Bilibili Gateway"
    version: "v1.0.0"
    description: "Bilibili微服务网关"

# SpringDoc OpenAPI 配置 - 聚合所有微服务的API文档
springdoc:
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    config-url: /v3/api-docs/swagger-config
    urls:
      - url: /v3/api-docs
        name: Gateway API
      - url: /api/v1/auth/v3/api-docs
        name: Auth Service
      - url: /api/v1/user/v3/api-docs
        name: User Service
  api-docs:
    enabled: true
    path: /v3/api-docs
  group-configs:
    - group: auth
      paths-to-match: /api/v1/auth/**
    - group: user
      paths-to-match: /api/v1/user/**





# 应用信息
info:
  app:
    name: ${app.gateway.name}
    version: ${app.gateway.version}
    description: ${app.gateway.description}
  build:
    time: "@maven.build.timestamp@"
  java:
    version: "@java.version@"



# 日志配置 - 减少启动冗余输出
logging:
  level:
    root: WARN
    com.bilibili.gateway: INFO
    # 减少Spring框架日志
    org.springframework: WARN
    org.springframework.boot: WARN
    org.springframework.web: WARN
    org.springframework.cloud: WARN
    org.springframework.cloud.gateway: WARN
    # 减少数据库和Redis日志
    org.springframework.data.redis: WARN
    io.lettuce: WARN
    reactor.netty: WARN
    # 减少Nacos日志
    com.alibaba.nacos: ERROR
    com.alibaba.cloud.nacos: ERROR
    # 屏蔽LoadBalancer警告
    org.springframework.beans.factory.support.DefaultListableBeanFactory$BeanPostProcessorChecker: ERROR
    org.springframework.context.annotation.ConfigurationClassPostProcessor$ImportAwareBeanPostProcessor: ERROR
    # Gateway特有组件日志优化
    org.springframework.cloud.gateway.filter: WARN
    org.springframework.cloud.gateway.route: WARN
    org.springframework.cloud.gateway.handler: WARN
    org.springframework.web.reactive: WARN
    org.springframework.http.server.reactive: WARN

  # 简化日志格式
  pattern:
    console: "%clr(%d{HH:mm:ss.SSS}){faint} %clr(%5p) %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} : %m%n"
