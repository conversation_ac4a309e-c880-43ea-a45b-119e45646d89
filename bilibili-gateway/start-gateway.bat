@echo off
echo 启动网关服务...

REM 设置JVM参数解决请求头过大问题
set JAVA_OPTS=-Dio.netty.http.maxInitialLineLength=32768
set JAVA_OPTS=%JAVA_OPTS% -Dio.netty.http.maxHeaderSize=262144
set JAVA_OPTS=%JAVA_OPTS% -Dio.netty.http.maxChunkSize=16384
set JAVA_OPTS=%JAVA_OPTS% -Dreactor.netty.http.server.maxInitialLineLength=32768
set JAVA_OPTS=%JAVA_OPTS% -Dreactor.netty.http.server.maxHeaderSize=262144
set JAVA_OPTS=%JAVA_OPTS% -Dreactor.netty.http.client.maxInitialLineLength=32768
set JAVA_OPTS=%JAVA_OPTS% -Dreactor.netty.http.client.maxHeaderSize=262144

echo JVM参数: %JAVA_OPTS%

REM 启动应用
mvn spring-boot:run -Dspring-boot.run.jvmArguments="%JAVA_OPTS%"

pause
