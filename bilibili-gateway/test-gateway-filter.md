# 🔧 网关JWT过滤器测试指南

## 🚀 修复内容

已成功合并两个JWT过滤器，解决了冲突问题：

### ✅ 修复要点
1. **删除重复过滤器**：移除了 `JwtAuthFilter.java`
2. **完善白名单**：统一了白名单路径配置
3. **改进路径匹配**：支持精确匹配、前缀匹配和通配符匹配
4. **优化日志**：添加了详细的调试日志

### 📋 白名单路径
```
/api/v1/auth/login-by-password
/api/v1/auth/login-by-code
/api/v1/auth/send-code
/api/v1/auth/refresh-token
/api/v1/auth/logout
/api/v1/auth/register
/api/v1/auth/wechat
/api/v1/auth/health
/api/v1/user/test
/actuator
/doc.html          ← 关键：Knife4j文档页面
/api/v1/doc.html
/docs
/swagger-ui
/v3/api-docs
/webjars
/favicon.ico
```

## 🧪 测试步骤

### 1. 重启网关服务
```bash
# 停止当前网关服务 (Ctrl+C)
cd bilibili-gateway
mvn spring-boot:run
```

### 2. 验证网关启动
```bash
# 检查8080端口
netstat -ano | findstr :8080

# 访问健康检查
curl http://localhost:8080/actuator/health
```

### 3. 测试Knife4j访问
访问：**http://localhost:8080/doc.html**

**预期结果**：
- ✅ 应该能正常访问，不再出现401错误
- ✅ 能看到完整的API文档界面
- ✅ 包含认证服务和用户服务的接口

### 4. 完整测试流程

#### 步骤1：登录获取JWT
1. 在网关文档中找到 `POST /api/v1/auth/login-by-password`
2. 输入登录信息：
```json
{
  "account": "你的手机号或用户名",
  "password": "你的密码",
  "remember": false,
  "deviceInfo": {
    "deviceName": "测试设备",
    "deviceType": "web"
  }
}
```
3. 复制响应中的 `accessToken`

#### 步骤2：配置认证
1. 点击右上角 🔒 **Authorize** 按钮
2. 输入：`Bearer 你的accessToken`
3. 点击 **Authorize**

#### 步骤3：测试用户接口
1. 找到 `GET /api/v1/user/me`
2. 点击 **Try it out**
3. 点击 **Execute**

**预期结果**：
```json
{
  "code": 200,
  "message": "获取用户信息成功",
  "data": {
    "uid": 123456,
    "username": "user123",
    "nickname": "小明",
    // ... 其他用户信息
  },
  "timestamp": "2025-07-30T09:05:14.878399800Z"
}
```

## 🔍 调试信息

### 查看网关日志
网关启动后，访问接口时应该能看到类似日志：

```
INFO  - 网关处理请求: GET /doc.html
INFO  - 路径 /doc.html 在白名单中，跳过JWT认证

INFO  - 网关处理请求: POST /api/v1/auth/login-by-password  
INFO  - 路径 /api/v1/auth/login-by-password 在白名单中，跳过JWT认证

INFO  - 网关处理请求: GET /api/v1/user/me
INFO  - 路径 /api/v1/user/me 不在白名单中，需要JWT认证
DEBUG - JWT认证成功，用户: user123 (123456), 转发到微服务
```

### 用户服务日志
用户服务应该能收到带有用户信息的请求头：

```
INFO  - 获取用户信息: userId=123456, username=user123
INFO  - 获取用户信息成功
```

## 🐛 故障排查

### 如果仍然出现401错误

1. **检查网关启动状态**
```bash
curl http://localhost:8080/actuator/health
```

2. **检查白名单配置**
查看网关日志，确认路径匹配逻辑

3. **清除浏览器缓存**
按 Ctrl+F5 强制刷新

4. **检查端口冲突**
确保8080端口没有被其他服务占用

### 如果JWT认证失败

1. **检查token格式**
确保格式为：`Bearer eyJhbGciOiJIUzUxMiJ9...`

2. **检查token有效期**
重新登录获取新的token

3. **检查服务间通信**
确保认证服务和用户服务都正常运行

## 📊 测试检查清单

- [ ] 网关服务启动成功 (8080端口)
- [ ] 认证服务运行正常 (8090端口)  
- [ ] 用户服务运行正常 (8091端口)
- [ ] 能访问 http://localhost:8080/doc.html
- [ ] 能看到完整的API文档
- [ ] 登录接口返回JWT token
- [ ] 用户接口认证成功
- [ ] 返回正确的用户信息
- [ ] 网关日志显示正常
- [ ] 用户服务日志显示正常

## 🎯 成功标志

修复成功后，您应该能够：

1. ✅ 正常访问 http://localhost:8080/doc.html
2. ✅ 看到统一的API文档界面
3. ✅ 通过认证接口获取JWT token
4. ✅ 使用JWT token成功调用用户接口
5. ✅ 获取到正确的用户信息
6. ✅ 不再看到"用户未认证"的警告日志

请按照这个流程测试，如果还有问题请告诉我具体的错误信息！
